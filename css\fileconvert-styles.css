/* File Converter Styles */

.hero-section {
  background: var(--card-bg);
  border-radius: 15px;
  padding: 60px 40px;
  margin-bottom: 30px;
  position: relative;
  overflow: hidden;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.hero-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 0;
  pointer-events: none;
  animation: backgroundShift 20s ease-in-out infinite alternate;
}


.hero-content {
  position: relative;
  z-index: 1;
  text-align: center;
  animation: fadeInUp 0.8s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.subtitle {
  font-size: 1.3rem;
  color: var(--accent-light);
  margin-bottom: 20px;
  font-weight: 400;
  opacity: 0.9;
}

.hero-description {
  font-size: 1.1rem;
  color:var(--accent-light);
  max-width: 700px;
  margin: 0 auto;
  line-height: 1.7;
  opacity: 0.85;
}

/* Format Selector Section */
.format-selector-section {
  background-color: var(--card-bg);
  border-radius: 15px;
  padding: 30px;
  margin-bottom: 30px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
  border: 1px solid var(--border-color);
}

.format-selector-container {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 20px;
  flex-wrap: wrap;
}

.convert-text, .to-text {
  font-size: 1.2rem;
  color: var(--text-color);
  font-weight: 500;
}

.format-dropdown {
  position: relative;
  min-width: 180px;
}

.format-btn {
  background-color: var(--secondary-color);
  border: 2px solid var(--border-color);
  border-radius: 8px;
  padding: 12px 16px;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 1rem;
  color: var(--text-color);
}

.format-btn:hover {
  border-color: var(--highlight-color);
  background-color: var(--card-highlight);
}

.format-btn.active {
  border-color: var(--highlight-color);
  background-color: var(--card-highlight);
}

.format-text {
  font-weight: 500;
}

.format-dropdown-content {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background-color: var(--secondary-color);
  border: 1px solid var(--border-color);
  border-radius: 8px;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  display: none;
  max-height: 300px;
  overflow: hidden;
  margin-top: 5px;
}

.format-dropdown-content.show {
  display: block;
  animation: dropdownFadeIn 0.3s ease;
}

@keyframes dropdownFadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.format-search {
  position: relative;
  padding: 15px;
  border-bottom: 1px solid var(--border-color);
}

.format-search input {
  width: 100%;
  padding: 10px 35px 10px 12px;
  border: 1px solid var(--border-color);
  border-radius: 6px;
  background-color: var(--primary-color);
  color: var(--text-color);
  font-size: 0.9rem;
}

.format-search input:focus {
  outline: none;
  border-color: var(--highlight-color);
}

.format-search i {
  position: absolute;
  right: 25px;
  top: 50%;
  transform: translateY(-50%);
  color: var(--accent-light);
}

.format-list {
  max-height: 200px;
  overflow-y: auto;
  padding: 5px 0;
}

.format-item {
  padding: 12px 15px;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 10px;
  color: var(--text-color);
}

.format-item:hover {
  background-color: var(--card-highlight);
}

.format-item i {
  color: var(--accent-color);
  width: 20px;
  text-align: center;
}

.format-item.hidden {
  display: none;
}

/* Upload Box Styles */
.upload-box {
  background-color: var(--card-bg);
  border-radius: 15px;
  padding: 40px;
  margin-bottom: 30px;
  text-align: center;
  border: 2px dashed var(--border-color);
  transition: all 0.3s ease;
  position: relative;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
}

.upload-box.drag-over {
  border-color: var(--highlight-color);
  background-color: var(--card-highlight);
}

.upload-icon {
  font-size: 3rem;
  color: var(--accent-color);
  margin-bottom: 20px;
}

.upload-label {
  display: inline-block;
  background-color: var(--highlight-color);
  color: black;
  padding: 12px 25px;
  border-radius: 30px;
  cursor: pointer;
  font-weight: 600;
  transition: all 0.3s ease;
  margin-bottom: 15px;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
}

.upload-label:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 15px rgba(0, 0, 0, 0.15);
}

.upload-label i {
  margin-right: 8px;
}

input[type="file"] {
  display: none;
}

.drag-text {
  color: var(--text-color);
  margin-bottom: 20px;
  font-size: 0.95rem;
}

.upload-info {
  display: flex;
  justify-content: center;
  gap: 20px;
  margin-top: 20px;
  flex-wrap: wrap;
}

.upload-info p {
  color: var(--accent-light);
  font-size: 0.9rem;
  display: flex;
  align-items: center;
  gap: 5px;
}

/* File Details Card */
.details-card {
  background-color: var(--card-bg);
  border-radius: 10px;
  padding: 0;
  margin-top: 25px;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.05);
  overflow: hidden;
  display: none;
  text-align: left;
  border: 1px solid var(--border-color);
}

.details-header {
  background-color: var(--secondary-color);
  padding: 12px 20px;
  font-weight: 600;
  color: var(--text-color);
  display: flex;
  align-items: center;
  gap: 8px;
  border-bottom: 1px solid var(--border-color);
}

.details-content {
  padding: 15px 20px;
}

.details-content p {
  margin: 8px 0;
  display: flex;
  align-items: center;
  gap: 8px;
  color: var(--text-color);
}

.details-content p i {
  color: var(--accent-color);
  width: 20px;
  text-align: center;
}

/* Post-upload format selection */
.post-upload-format {
  background-color: var(--card-highlight);
  border-radius: 10px;
  padding: 20px;
  margin-top: 20px;
  border: 1px solid var(--highlight-color);
  animation: slideDown 0.5s ease;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.post-upload-format h3 {
  margin: 0 0 15px 0;
  color: var(--text-color);
  font-size: 1.1rem;
  display: flex;
  align-items: center;
  gap: 8px;
}

.post-upload-format .format-dropdown {
  min-width: 250px;
}

/* Conversion Options */
.conversion-options {
  background-color: var(--card-bg);
  border-radius: 15px;
  padding: 30px;
  margin-bottom: 30px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
  border: 1px solid var(--border-color);
}

.options-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 25px;
  flex-wrap: wrap;
  gap: 15px;
}

.options-header h3 {
  font-size: 1.2rem;
  color: var(--text-color);
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 0;
}

/* Format Selection */
.format-selection {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
  gap: 15px;
  margin-top: 20px;
}

.format-option {
  background-color: var(--secondary-color);
  border-radius: 10px;
  padding: 15px;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 1px solid var(--border-color);
}

.format-option:hover {
  transform: translateY(-3px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  border-color: var(--highlight-color);
}

.format-option.active {
  background-color: var(--card-highlight);
  border-color: var(--highlight-color);
}

.format-icon {
  font-size: 2rem;
  color: var(--accent-color);
  margin-bottom: 10px;
}

.format-option.active .format-icon {
  color: var(--highlight-color);
}

.format-name {
  font-size: 0.9rem;
  font-weight: 600;
  color: var(--text-color);
}

/* Comment Box */
.comment-box {
  background-color: #ffebee; /* Light red background */
  border-left: 4px solid #f44336; /* Red border */
  color: #d32f2f; /* Dark red text */
  padding: 15px 20px;
  margin: 25px 0;
  border-radius: 5px;
  box-shadow: 0 2px 8px rgba(244, 67, 54, 0.1);
}

.comment-box h3 {
  margin-top: 0;
  margin-bottom: 10px;
  font-size: 1.1rem;
  display: flex;
  align-items: center;
  gap: 8px;
}

.comment-box p {
  margin: 0;
  line-height: 1.5;
}

/* Action Button */
.action-button {
  background-color: var(--highlight-color);
  color: black;
  border: none;
  padding: 12px 25px;
  border-radius: 30px;
  font-weight: 600;
  font-size: 1rem;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  margin: 0 auto 30px;
  transition: all 0.3s ease;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
}

.action-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 15px rgba(0, 0, 0, 0.15);
}

.action-button.secondary {
  background-color: var(--secondary-color);
  color: var(--text-color);
}

.action-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

/* How It Works Section */
.how-it-works {
  background-color: var(--card-bg);
  border-radius: 15px;
  padding: 40px;
  margin-top: 40px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
  border: 1px solid var(--border-color);
}

.section-header {
  text-align: center;
  margin-bottom: 50px;
}

.how-it-works h2 {
  color: var(--text-color);
  margin-bottom: 15px;
  font-size: 2.2rem;
  font-weight: 700;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
}

.section-subtitle {
  color: var(--accent-light);
  font-size: 1.1rem;
  margin: 0;
  opacity: 0.9;
}

.steps-container {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
  gap: 25px;
  margin-bottom: 30px;
}

.step {
  text-align: center;
  padding: 20px;
  border-radius: 10px;
  background-color: var(--secondary-color);
  transition: all 0.3s ease;
  border: 1px solid var(--border-color);
}

.step:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
}

.step-icon {
  position: relative;
  width: 70px;
  height: 70px;
  background-color: var(--card-highlight);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 15px;
  font-size: 1.5rem;
  color: var(--highlight-color);
}

.step-number {
  position: absolute;
  top: -5px;
  right: -5px;
  width: 25px;
  height: 25px;
  background-color: var(--highlight-color);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.8rem;
  font-weight: 700;
  color: black;
}

.step h3 {
  margin-bottom: 10px;
  color: var(--text-color);
  font-size: 1.1rem;
}

.step p {
  color: var(--accent-light);
  font-size: 0.95rem;
  line-height: 1.6;
  margin: 0 0 15px 0;
}

.step-features {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
  margin-top: 10px;
}

.feature-tag {
  background-color: var(--card-highlight);
  color: var(--text-color);
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 0.75rem;
  display: flex;
  align-items: center;
  gap: 4px;
  border: 1px solid var(--border-color);
  opacity: 0.8;
}

.feature-tag i {
  font-size: 0.7rem;
  color: var(--highlight-color);
}

/* Features Highlight */
.features-highlight {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin: 40px 0;
  padding: 30px;
  background-color: var(--secondary-color);
  border-radius: 12px;
  border: 1px solid var(--border-color);
}

.feature-item {
  text-align: center;
  padding: 20px 15px;
  background-color: var(--card-highlight);
  border-radius: 10px;
  border: 1px solid var(--border-color);
  transition: all 0.3s ease;
}

.feature-item:hover {
  transform: translateY(-3px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.feature-item i {
  font-size: 2rem;
  color: var(--highlight-color);
  margin-bottom: 10px;
  display: block;
}

.feature-item h4 {
  color: var(--text-color);
  margin: 0 0 8px 0;
  font-size: 1rem;
  font-weight: 600;
}

.feature-item p {
  color: var(--accent-light);
  font-size: 0.85rem;
  margin: 0;
  opacity: 0.9;
}

.tech-info {
  background-color: var(--secondary-color);
  border-radius: 10px;
  padding: 20px;
  border: 1px solid var(--border-color);
}

.tech-info h3 {
  display: flex;
  align-items: center;
  gap: 8px;
  color: var(--text-color);
  margin-top: 0;
  margin-bottom: 10px;
  font-size: 1.1rem;
}

.tech-info p {
  color: var(--accent-light);
  font-size: 0.95rem;
  line-height: 1.6;
  margin: 0 0 20px 0;
}

.blog-promo {
  margin-top: 25px;
  padding: 15px 20px;
  background-color: rgba(255, 255, 255, 0.05);
  border-radius: 10px;
  border: 1px solid var(--border-color);
  display: inline-block;
}

.blog-promo p {
  margin: 0;
  font-size: 1.05rem;
}

.blog-promo i {
  color: var(--highlight-color);
  margin-right: 8px;
}

.blog-promo a {
  color: var(--highlight-color);
  font-weight: 600;
  text-decoration: none;
  border-bottom: 1px dotted var(--highlight-color);
  transition: all 0.3s ease;
}

.blog-promo a:hover {
  border-bottom: 1px solid var(--highlight-color);
}

/* Supported Categories */
.supported-categories {
  margin-top: 30px;
  padding-top: 25px;
  border-top: 1px solid var(--border-color);
}

.supported-categories h4 {
  color: var(--text-color);
  margin: 0 0 20px 0;
  font-size: 1.1rem;
  display: flex;
  align-items: center;
  gap: 8px;
}

.category-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
  gap: 15px;
}

.category-item {
  background-color: var(--card-highlight);
  border: 1px solid var(--border-color);
  border-radius: 10px;
  padding: 15px;
  text-align: center;
  transition: all 0.3s ease;
}

.category-item:hover {
  transform: translateY(-2px);
  border-color: var(--highlight-color);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.category-item i {
  font-size: 1.8rem;
  color: var(--highlight-color);
  margin-bottom: 8px;
  display: block;
}

.category-item span {
  display: block;
  color: var(--text-color);
  font-weight: 600;
  margin-bottom: 5px;
  font-size: 0.95rem;
}

.category-item small {
  color: var(--accent-light);
  font-size: 0.8rem;
  opacity: 0.8;
}

/* Responsive Adjustments */

/* Large tablets and small desktops */
@media (max-width: 1024px) {
  .container {
    padding: 0 20px;
  }

  .how-it-works {
    padding: 50px 30px;
  }

  .features-highlight {
    grid-template-columns: repeat(2, 1fr);
    padding: 25px;
  }

  .category-grid {
    grid-template-columns: repeat(auto-fit, minmax(160px, 1fr));
  }
}

/* Tablets */
@media (max-width: 768px) {
  .hero-section {
    padding: 40px 20px;
    margin-bottom: 20px;
  }

  .file-title {
    font-size: 2.2rem;
    letter-spacing: 0.5px;
  }

  .subtitle {
    font-size: 1.1rem;
  }

  .hero-description {
    font-size: 1rem;
    padding: 0 10px;
  }

  .upload-box {
    padding: 30px 20px;
  }

  .conversion-options {
    padding: 25px 20px;
  }

  .format-selection {
    grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
  }

  .how-it-works {
    padding: 40px 25px;
  }

  .section-header {
    margin-bottom: 40px;
  }

  .how-it-works h2 {
    font-size: 1.9rem;
  }

  .steps-container {
    grid-template-columns: 1fr;
    gap: 30px;
  }

  .features-highlight {
    grid-template-columns: repeat(2, 1fr);
    gap: 15px;
    padding: 20px;
    margin: 30px 0;
  }

  .category-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 12px;
  }

  .format-selector-container {
    flex-direction: column;
    gap: 15px;
  }

  .format-dropdown {
    min-width: 100%;
    max-width: 300px;
  }

  .convert-text, .to-text {
    font-size: 1.1rem;
  }

  .format-selector-section {
    padding: 25px 20px;
  }

  .post-upload-format .format-dropdown {
    min-width: 100%;
  }
}

/* Mobile phones */
@media (max-width: 480px) {
  .container {
    padding: 0 15px;
  }

  .hero-section {
    padding: 30px 15px;
  }

  .file-title {
    font-size: 1.8rem;
  }

  .subtitle {
    font-size: 1rem;
  }

  .hero-description {
    font-size: 0.9rem;
  }

  .format-selector-section {
    padding: 20px 15px;
  }

  .upload-box {
    padding: 25px 15px;
  }

  .how-it-works {
    padding: 30px 20px;
  }

  .how-it-works h2 {
    font-size: 1.6rem;
    flex-direction: column;
    gap: 5px;
  }

  .section-subtitle {
    font-size: 1rem;
  }

  .step {
    padding: 20px 15px;
  }

  .step-icon {
    width: 50px;
    height: 50px;
  }

  .step-icon i {
    font-size: 1.2rem;
  }

  .step-number {
    width: 20px;
    height: 20px;
    font-size: 0.7rem;
  }

  .step h3 {
    font-size: 1rem;
  }

  .step p {
    font-size: 0.85rem;
  }

  .feature-tag {
    font-size: 0.7rem;
    padding: 3px 6px;
  }

  .features-highlight {
    grid-template-columns: 1fr;
    gap: 12px;
    padding: 15px;
    margin: 25px 0;
  }

  .feature-item {
    padding: 15px 10px;
  }

  .feature-item i {
    font-size: 1.5rem;
  }

  .feature-item h4 {
    font-size: 0.9rem;
  }

  .feature-item p {
    font-size: 0.8rem;
  }

  .category-grid {
    grid-template-columns: 1fr;
    gap: 10px;
  }

  .category-item {
    padding: 12px;
  }

  .category-item i {
    font-size: 1.5rem;
  }

  .category-item span {
    font-size: 0.9rem;
  }

  .category-item small {
    font-size: 0.75rem;
  }

  .tech-info {
    padding: 15px;
  }

  .blog-promo {
    padding: 12px 15px;
  }

  .blog-promo p {
    font-size: 0.95rem;
  }
}

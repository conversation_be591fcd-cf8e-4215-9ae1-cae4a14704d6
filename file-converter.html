<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>File Converter - ImgNinja</title>
  <link rel="stylesheet" href="css/styles.css">
  <link rel="stylesheet" href="css/responsive.css">
  <link rel="stylesheet" href="css/fileconvert-styles.css">
  <!-- Add Poppins Font -->
  <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600;700&display=swap" rel="stylesheet">
  <!-- Add Font Awesome Icons -->
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
  <!-- Favicon -->
  <link rel="icon" href="assets/favicon-imgNinja.png" type="image/png">
</head>
<body>
  <!-- Navigation Bar -->
  <nav>
    <div class="logo">
      <a href="index.html">
        <img src="assets/logo-imgNinja.png" alt="ImgNinja Logo" width="170" height="90">
      </a>
    </div>
    <div class="menu-toggle" id="mobile-menu">
      <span class="bar"></span>
      <span class="bar"></span>
      <span class="bar"></span>
    </div>
    <ul class="nav-links">
      <li><a href="index.html"><i class="fas fa-home"></i> Home</a></li>
      <li><a href="index.html"><i class="fas fa-image"></i> Image Compress</a></li>
      <li><a href="file-converter.html"><i class="fas fa-file-alt"></i> File Converter</a></li>
      <li><a href="pages/about-us.html"><i class="fas fa-info-circle"></i> About</a></li>
    </ul>
  </nav>

  <!-- Main Content -->
  <div class="container">
    <div class="hero-section">
      <div class="hero-content">
        <h1>
          <span class="file-title">File Converter</span>
        </h1>
        <p class="subtitle">Convert files between different formats easily</p>
        <p class="hero-description">Our powerful file converter supports multiple formats and delivers high-quality conversions with just a few clicks.</p>
      </div>
    </div>

    <!-- Comment Box (Red) -->
    <div class="comment-box">
      <h3><i class="fas fa-exclamation-circle"></i> Under Development</h3>
      <p>This file converter tool is currently under development. Some features may not be fully functional yet. We're working hard to bring you a complete and reliable file conversion experience soon!</p>
    </div>

    <div class="upload-box" id="drop-area">
      <div class="upload-icon">
        <i class="fas fa-file-alt"></i>
      </div>
      <input type="file" id="file-upload">
      <label for="file-upload" class="upload-label"><i class="fas fa-cloud-upload-alt"></i> Choose a File</label>
      <p class="drag-text">or drag and drop your file here</p>
      <div class="upload-info">
        <p class="formats"><i class="fas fa-file"></i> Multiple Formats Supported</p>
        <p class="max-size"><i class="fas fa-weight-hanging"></i> Max File Size: 100MB</p>
      </div>
      <div id="file-details" class="details-card">
        <div class="details-header">
          <i class="fas fa-info-circle"></i> File Details
        </div>
        <div class="details-content">
          <p id="file-name"><i class="fas fa-file"></i> Name: <span></span></p>
          <p id="file-size"><i class="fas fa-database"></i> Size: <span></span></p>
          <p id="file-type"><i class="fas fa-file-code"></i> Type: <span></span></p>
        </div>
      </div>
    </div>

    <!-- Conversion Options -->
    <div class="conversion-options">
      <div class="options-header">
        <h3><i class="fas fa-exchange-alt"></i> Convert To</h3>
      </div>
      
      <div class="format-selection">
        <!-- Document Formats -->
        <div class="format-option" data-format="pdf">
          <div class="format-icon">
            <i class="fas fa-file-pdf"></i>
          </div>
          <div class="format-name">PDF</div>
        </div>
        <div class="format-option" data-format="docx">
          <div class="format-icon">
            <i class="fas fa-file-word"></i>
          </div>
          <div class="format-name">DOCX</div>
        </div>
        <div class="format-option" data-format="txt">
          <div class="format-icon">
            <i class="fas fa-file-alt"></i>
          </div>
          <div class="format-name">TXT</div>
        </div>
        
        <!-- Image Formats -->
        <div class="format-option" data-format="jpg">
          <div class="format-icon">
            <i class="fas fa-file-image"></i>
          </div>
          <div class="format-name">JPG</div>
        </div>
        <div class="format-option" data-format="png">
          <div class="format-icon">
            <i class="fas fa-file-image"></i>
          </div>
          <div class="format-name">PNG</div>
        </div>
        <div class="format-option" data-format="webp">
          <div class="format-icon">
            <i class="fas fa-file-image"></i>
          </div>
          <div class="format-name">WEBP</div>
        </div>
        
        <!-- Audio Formats -->
        <div class="format-option" data-format="mp3">
          <div class="format-icon">
            <i class="fas fa-file-audio"></i>
          </div>
          <div class="format-name">MP3</div>
        </div>
        <div class="format-option" data-format="wav">
          <div class="format-icon">
            <i class="fas fa-file-audio"></i>
          </div>
          <div class="format-name">WAV</div>
        </div>
        
        <!-- Video Formats -->
        <div class="format-option" data-format="mp4">
          <div class="format-icon">
            <i class="fas fa-file-video"></i>
          </div>
          <div class="format-name">MP4</div>
        </div>
        <div class="format-option" data-format="avi">
          <div class="format-icon">
            <i class="fas fa-file-video"></i>
          </div>
          <div class="format-name">AVI</div>
        </div>
        
        <!-- Archive Formats -->
        <div class="format-option" data-format="zip">
          <div class="format-icon">
            <i class="fas fa-file-archive"></i>
          </div>
          <div class="format-name">ZIP</div>
        </div>
        <div class="format-option" data-format="rar">
          <div class="format-icon">
            <i class="fas fa-file-archive"></i>
          </div>
          <div class="format-name">RAR</div>
        </div>
      </div>
    </div>

    <button id="convert-btn" class="action-button"><i class="fas fa-exchange-alt"></i> Convert File</button>

    <div class="action-buttons">
      <button id="download-btn" class="action-button" style="display: none;"><i class="fas fa-download"></i> Download Converted File</button>
      <button id="new-file-btn" class="action-button secondary" style="display: none;"><i class="fas fa-redo-alt"></i> Convert Another File</button>
    </div>

    <!-- How It Works Section -->
    <div class="how-it-works">
      <h2><i class="fas fa-cogs"></i> How It Works</h2>
      <div class="steps-container">
        <div class="step">
          <div class="step-icon">
            <i class="fas fa-upload"></i>
            <div class="step-number">1</div>
          </div>
          <h3>Upload Your File</h3>
          <p>Select a file from your device or drag and drop it into the upload area.</p>
        </div>
        <div class="step">
          <div class="step-icon">
            <i class="fas fa-exchange-alt"></i>
            <div class="step-number">2</div>
          </div>
          <h3>Choose Format</h3>
          <p>Select the output format you want to convert your file to.</p>
        </div>
        <div class="step">
          <div class="step-icon">
            <i class="fas fa-magic"></i>
            <div class="step-number">3</div>
          </div>
          <h3>Convert</h3>
          <p>Click the "Convert File" button to start the conversion process. Our advanced algorithms will handle the rest.</p>
        </div>
        <div class="step">
          <div class="step-icon">
            <i class="fas fa-download"></i>
            <div class="step-number">4</div>
          </div>
          <h3>Download</h3>
          <p>Once conversion is complete, download your converted file. It's that simple!</p>
        </div>
      </div>
      <div class="tech-info">
        <h3><i class="fas fa-microchip"></i> Our Technology</h3>
        <p>ImgNinja uses advanced conversion algorithms that preserve the quality and integrity of your files. Our technology works entirely in your browser - your files are never uploaded to any server, ensuring complete privacy and security.</p>
      </div>
    </div>
  </div>

  <!-- Footer -->
  <footer>
    <div class="footer-content">
      <!-- Column 1: Logo and About -->
      <div class="footer-column">
        <a href="index.html" class="footer-logo">
          <img src="assets/logo-imgNinja.png" alt="ImgNinja Logo" width="200" height="110">
        </a>
        <p class="footer-description">
          ImgNinja provides powerful, free online tools for image and document compression, helping you optimize your files without sacrificing quality.
        </p>
        <div class="social-links">
          <a href="#" class="social-link" title="Facebook"><i class="fab fa-facebook-f"></i></a>
          <a href="#" class="social-link" title="Twitter"><i class="fab fa-twitter"></i></a>
          <a href="#" class="social-link" title="Instagram"><i class="fab fa-instagram"></i></a>
          <a href="#" class="social-link" title="LinkedIn"><i class="fab fa-linkedin-in"></i></a>
        </div>
      </div>
      
      <!-- Column 2: Quick Links -->
      <div class="footer-column">
        <h3 class="footer-heading">Quick Links</h3>
        <div class="footer-links">
          <a href="index.html"><i class="fas fa-home"></i> Home</a>
          <a href="blog/index.html"><i class="fas fa-blog"></i> Blog</a>
          <a href="pages/about-us.html"><i class="fas fa-info-circle"></i> About Us</a>
          <a href="pages/contact-us.html"><i class="fas fa-envelope"></i> Contact Us</a>
          <a href="#"><i class="fas fa-question-circle"></i> Help & Support</a>
        </div>
      </div>
      
      <!-- Column 3: Legal -->
      <div class="footer-column">
        <h3 class="footer-heading">Legal</h3>
        <div class="footer-links">
          <a href="pages/privacy-policy.html"><i class="fas fa-shield-alt"></i> Privacy Policy</a>
          <a href="pages/terms-conditions.html"><i class="fas fa-file-contract"></i> Terms & Conditions</a>
          <a href="pages/dmca.html"><i class="fas fa-copyright"></i> DMCA</a>
          <a href="#"><i class="fas fa-cookie"></i> Cookie Policy</a>
        </div>
      </div>
      
      <!-- Column 4: Tools -->
      <div class="footer-column">
        <h3 class="footer-heading">Our Tools</h3>
        <div class="footer-links">
          <a href="index.html"><i class="fas fa-image"></i> Image Compress</a>
          <a href="file-converter.html"><i class="fas fa-file-alt"></i> File Converter</a>
          <a href="#"><i class="fas fa-crop-alt"></i> Image Resizer</a>
          <a href="#"><i class="fas fa-object-group"></i> Image Editor</a>
        </div>
      </div>
      
      <!-- Footer Bottom -->
      <div class="footer-bottom">
        <p class="copyright">&copy; 2025 ImgNinja. All rights reserved. | Made by <a href="https://totalsolution.42web.io/" target="_blank">Total Solution</a></p>
      </div>
    </div>
    
    <!-- Back to Top Button -->
    <a href="#" class="back-to-top" title="Back to Top"><i class="fas fa-arrow-up"></i></a>
  </footer>
  
  <!-- Back to Top Script -->
  <script>
    // Back to top button functionality
    document.addEventListener('DOMContentLoaded', function() {
      const backToTopButton = document.querySelector('.back-to-top');
      
      // Show/hide button based on scroll position
      window.addEventListener('scroll', function() {
        if (window.pageYOffset > 300) {
          backToTopButton.classList.add('visible');
        } else {
          backToTopButton.classList.remove('visible');
        }
      });
      
      // Smooth scroll to top when clicked
      backToTopButton.addEventListener('click', function(e) {
        e.preventDefault();
        window.scrollTo({
          top: 0,
          behavior: 'smooth'
        });
      });
    });
  </script>

  <!-- App Scripts -->
  <script src="js/script.js"></script>
  <script src="js/fileconvert-script.js"></script>
</body>
</html>
